'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { X, Users, MessageCircle, Building2, CheckSquare } from 'lucide-react';
import { appTheme } from '@/app/theme';
import { chatApi, dataApi } from '@/services/chatService';
import useUserStore from '@/store/userStore';
import { useAuth } from '@/hooks/useAuth';

interface User {
  id: number;
  firstName?: string;
  lastName?: string;
  name?: string;
  email: string;
  imageUrl?: string;
  departmentName?: string;
  organizationName?: string;
  isLeader?: boolean;
  isAdmin?: boolean;
  isOwner?: boolean;
}

interface Department {
  id: number;
  name: string;
  description?: string;
  organizationId?: number;
}

interface Organization {
  id: number;
  name: string;
  description?: string;
}

interface Task {
  id: number;
  taskTitle: string;
  taskDescription?: string;
  assignedToUserId: number;
  statusId: number;
  status?: {
    id: number;
    displayName: string;
  };
  assignedToUser?: {
    id: number;
    firstName: string;
    lastName: string;
  };
}

interface CreateChatModalProps {
  isOpen: boolean;
  chatType: 'private' | 'task' | 'department' | 'organization';
  taskId?: number;
  onClose: () => void;
  onChatCreated: (chat: any) => void;
}

const ModalOverlay = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: ${props => (props.$isOpen ? 'flex' : 'none')};
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  width: 90vw;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
`;

const ModalHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${appTheme.spacing.lg};
  border-bottom: 1px solid ${appTheme.colors.border};
`;

const ModalTitle = styled.h2`
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  color: ${appTheme.colors.text.primary};
  font-size: 18px;
`;

const CloseButton = styled.button`
  border: none;
  background: none;
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.sm};

  &:hover {
    background: ${appTheme.colors.background.lighter};
    color: ${appTheme.colors.text.primary};
  }
`;

const ModalBody = styled.div`
  padding: ${appTheme.spacing.lg};
  max-height: 60vh;
  overflow-y: auto;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: 14px;
  outline: none;
  margin-bottom: ${appTheme.spacing.md};

  &:focus {
    border-color: ${appTheme.colors.primary};
  }
`;

const ItemList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.xs};
`;

const ItemCard = styled.div<{ $isSelected?: boolean }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};
  padding: ${appTheme.spacing.md};
  border: 1px solid
    ${props => (props.$isSelected ? appTheme.colors.primary : appTheme.colors.border)};
  border-radius: ${appTheme.borderRadius.md};
  cursor: pointer;
  background: ${props =>
    props.$isSelected ? appTheme.colors.primaryLight : appTheme.colors.background.light};
  transition: all 0.2s ease;

  &:hover {
    border-color: ${appTheme.colors.primary};
    background: ${appTheme.colors.primaryLight};
  }
`;

const Avatar = styled.div<{ $src?: string }>`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: ${props => (props.$src ? `url(${props.$src})` : appTheme.colors.background.light)};
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${appTheme.colors.text.light};
  font-weight: 600;
  flex-shrink: 0;
`;

const ItemInfo = styled.div`
  flex: 1;
`;

const ItemName = styled.div`
  font-weight: 600;
  color: ${appTheme.colors.text.primary};
  margin-bottom: 2px;
`;

const ItemDescription = styled.div`
  font-size: 12px;
  color: ${appTheme.colors.text.secondary};
`;

const ModalFooter = styled.div`
  padding: ${appTheme.spacing.lg};
  border-top: 1px solid ${appTheme.colors.border};
  display: flex;
  gap: ${appTheme.spacing.md};
  justify-content: flex-end;
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.lg};
  border: 1px solid
    ${props => (props.$variant === 'primary' ? appTheme.colors.primary : appTheme.colors.border)};
  border-radius: ${appTheme.borderRadius.md};
  background: ${props =>
    props.$variant === 'primary' ? appTheme.colors.primary : appTheme.colors.background.main};
  color: ${props =>
    props.$variant === 'primary' ? appTheme.colors.text.light : appTheme.colors.text.primary};
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover {
    opacity: 0.9;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ErrorMessage = styled.div`
  color: ${appTheme.colors.error.main};
  font-size: 14px;
  margin-bottom: ${appTheme.spacing.md};
  padding: ${appTheme.spacing.sm};
  background: ${appTheme.colors.error.light};
  border-radius: ${appTheme.borderRadius.sm};
`;

const LoadingSpinner = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xl};
  color: ${appTheme.colors.text.secondary};
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xl};
  color: ${appTheme.colors.text.secondary};
  text-align: center;
`;

const EmptyStateIcon = styled.div`
  margin-bottom: ${appTheme.spacing.md};
  opacity: 0.5;
`;

const EmptyStateText = styled.div`
  font-size: 14px;
`;

const FilterSection = styled.div`
  margin-bottom: ${appTheme.spacing.md};
`;

const FilterLabel = styled.label`
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: ${appTheme.colors.text.primary};
  margin-bottom: ${appTheme.spacing.xs};
`;

const FilterSelect = styled.select`
  width: 100%;
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: 14px;
  outline: none;
  background: ${appTheme.colors.background.main};
  color: ${appTheme.colors.text.primary};
  cursor: pointer;
  transition: border-color 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease;

  &:focus {
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    border-color: ${appTheme.colors.border};
    background-color: ${appTheme.colors.background.light};
    color: ${appTheme.colors.text.secondary};
  }
`;

const FilterGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${appTheme.spacing.md};
  margin-bottom: ${appTheme.spacing.md};
`;

export default function CreateChatModal({
  isOpen,
  chatType,
  taskId,
  onClose,
  onChatCreated,
}: CreateChatModalProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState('');
  const [currentUser, setCurrentUser] = useState<User | null>(null);

  // Get user data from store and auth
  const { userData } = useUserStore();
  const { getToken } = useAuth();

  // Filter states for private chat and department chat (for owners)
  const [selectedOrganizationId, setSelectedOrganizationId] = useState<number | null>(null);
  const [availableOrganizations, setAvailableOrganizations] = useState<Organization[]>([]);
  const [availableDepartments, setAvailableDepartments] = useState<Department[]>([]);

  // Enhanced search states for private chat (similar to Find Users modal)
  const [isSearching, setIsSearching] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  const [searchFilters, setSearchFilters] = useState({
    organizationId: '',
    departmentId: '',
    name: '',
  });

  // Data states
  const [users, setUsers] = useState<User[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [task, setTask] = useState<Task | null>(null);
  const [tasks, setTasks] = useState<Task[]>([]);

  // Load current user info
  useEffect(() => {
    const loadCurrentUser = async () => {
      try {
        const response = await dataApi.getCurrentUser();
        console.log('Current user response:', response);
        setCurrentUser(response.user);
      } catch (error) {
        console.error('Failed to load current user:', error);
      }
    };

    if (isOpen) {
      loadCurrentUser();
    }
  }, [isOpen]);

  // Load data based on chat type
  useEffect(() => {
    const loadData = async () => {
      if (!isOpen || !currentUser) return;

      setLoading(true);
      setError('');

      try {
        switch (chatType) {
          case 'private':
            // Load user's organizations for filtering (similar to Find Users modal)
            if (userData?.organizations && userData.organizations.length > 0) {
              // Use organizations from user store
              const userOrganizations = userData.organizations.map(org => ({
                id: org.id,
                name: org.name,
                description: org.description || undefined,
              }));
              setAvailableOrganizations(userOrganizations);

              // Set default to first organization and initialize search filters
              if (userOrganizations.length > 0) {
                const firstOrgId = userOrganizations[0].id.toString();
                setSearchFilters(prev => ({ ...prev, organizationId: firstOrgId }));

                // Fetch departments for the first organization
                try {
                  const token = await getToken();
                  if (token) {
                    const deptResponse = await fetch(`/api/v1/department?organizationId=${firstOrgId}`, {
                      headers: {
                        Authorization: `Bearer ${token}`,
                      },
                    });

                    if (deptResponse.ok) {
                      const deptData = await deptResponse.json();
                      setAvailableDepartments(deptData.departments || []);
                    }
                  }

                  // Trigger initial search with first organization
                  searchUsers({ organizationId: firstOrgId, departmentId: '', name: '' });
                } catch (err) {
                  console.error('Error setting up initial search for private chat:', err);
                }
              }
            } else {
              // Fallback to API call if no organizations in store
              const orgsResponse = await dataApi.getUserOrganizations(currentUser.id);
              const orgs = orgsResponse.organizations || [];
              setAvailableOrganizations(orgs);

              if (orgs.length > 0) {
                const firstOrgId = orgs[0].id.toString();
                setSearchFilters(prev => ({ ...prev, organizationId: firstOrgId }));

                // Fetch departments and trigger initial search
                try {
                  const token = await getToken();
                  if (token) {
                    const deptResponse = await fetch(`/api/v1/department?organizationId=${firstOrgId}`, {
                      headers: {
                        Authorization: `Bearer ${token}`,
                      },
                    });

                    if (deptResponse.ok) {
                      const deptData = await deptResponse.json();
                      setAvailableDepartments(deptData.departments || []);
                    }
                  }

                  // Trigger initial search with first organization
                  searchUsers({ organizationId: firstOrgId, departmentId: '', name: '' });
                } catch (err) {
                  console.error('Error setting up initial search for private chat:', err);
                }
              }
            }
            break;

          case 'task':
            if (taskId) {
              // Load specific task for existing taskId
              const taskResponse = await dataApi.getTask(taskId);
              setTask(taskResponse.task);
            } else {
              // Load user's accessible tasks for selection
              const tasksResponse = await dataApi.getAccessibleTasks();
              setTasks(tasksResponse.tasks || []);
            }
            break;

          case 'department':
            if (userData?.role?.isOwner) {
              // If user is owner, load organizations for filtering
              const orgsResponse = await dataApi.getUserOrganizations(currentUser.id);
              setAvailableOrganizations(orgsResponse.organizations || []);

              // Set default to first organization if available
              if (orgsResponse.organizations?.length > 0) {
                setSelectedOrganizationId(orgsResponse.organizations[0].id);
                // Load departments from first organization by default
                const deptsResponse = await dataApi.getOrganizationDepartments(
                  orgsResponse.organizations[0].id
                );
                setDepartments(deptsResponse.departments || []);
              }
            } else {
              // If user is not owner, use organizations from userData.organizations in userStore for filtering
              if (userData?.organizations && userData.organizations.length > 0) {
                // Use organizations from user store for filtering
                const userOrganizations = userData.organizations.map(org => ({
                  id: org.id,
                  name: org.name,
                  description: org.description || undefined,
                }));
                setAvailableOrganizations(userOrganizations);

                // Set default to first organization if available
                if (userOrganizations.length > 0) {
                  setSelectedOrganizationId(userOrganizations[0].id);
                  // Load departments from first organization by default
                  const deptsResponse = await dataApi.getOrganizationDepartments(
                    userOrganizations[0].id
                  );
                  setDepartments(deptsResponse.departments || []);
                }
              } else if (userData?.departments && userData.departments.length > 0) {
                // Use departments from user store if available and no organizations
                setDepartments(
                  userData.departments.map(dept => ({
                    id: dept.id,
                    name: dept.name,
                    description: dept.description || undefined,
                    organizationId: dept.organizationId, // Include organizationId for proper mapping
                  }))
                );
              } else {
                // Fallback to API call
                const userDeptsResponse = await dataApi.getUserDepartments(currentUser.id);
                setDepartments(userDeptsResponse.departments || []);
              }
            }
            break;

          case 'organization':
            if (userData?.role?.isOwner) {
              // If user is owner, load all organizations the user owns/has access to
              const orgsResponse = await dataApi.getUserOrganizations(currentUser.id);
              setOrganizations(orgsResponse.organizations || []);
            } else {
              // If user is not owner, use organizations from userData.organizations in userStore
              if (userData?.organizations && userData.organizations.length > 0) {
                // Use organizations from user store
                setOrganizations(
                  userData.organizations.map(org => ({
                    id: org.id,
                    name: org.name,
                    description: org.description || undefined,
                  }))
                );
              } else {
                // Fallback to API call if no organizations in store
                const orgsResponse = await dataApi.getUserOrganizations(currentUser.id);
                setOrganizations(orgsResponse.organizations || []);
              }
            }
            break;
        }
      } catch (error) {
        setError(`Failed to load ${chatType} data`);
        console.error('Failed to load data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [isOpen, chatType, taskId, currentUser]);

  // Handle department filtering for owners (non-private chat types)
  useEffect(() => {
    const loadFilteredData = async () => {
      if (!currentUser) return;

      if (chatType === 'department' && userData?.role?.isOwner) {
        // Handle department filtering for owners
        if (selectedOrganizationId) {
          setLoading(true);
          setError('');

          try {
            const deptsResponse = await dataApi.getOrganizationDepartments(selectedOrganizationId);
            setDepartments(deptsResponse.departments || []);
            setSelectedItems([]); // Clear selections when filters change
          } catch (error) {
            console.error('Failed to load organization departments:', error);
            setError('Failed to load departments');
          } finally {
            setLoading(false);
          }
        }
      }
    };

    if (selectedOrganizationId !== null && chatType !== 'private') {
      loadFilteredData();
    }
  }, [
    selectedOrganizationId,
    chatType,
    currentUser,
    userData?.role?.isOwner,
  ]);

  // Load departments when organization changes (for private chat only)
  useEffect(() => {
    const loadOrganizationDepartments = async () => {
      if (chatType !== 'private' || !selectedOrganizationId) {
        setAvailableDepartments([]);
        return;
      }

      try {
        const deptsResponse = await dataApi.getOrganizationDepartments(selectedOrganizationId);
        setAvailableDepartments(deptsResponse.departments || []);
      } catch (error) {
        console.error('Failed to load organization departments:', error);
        setAvailableDepartments([]);
      }
    };

    loadOrganizationDepartments();
  }, [selectedOrganizationId, chatType]);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSearchQuery('');
      setSelectedItems([]);
      setError('');
      setSelectedOrganizationId(null);
      setSelectedDepartmentId(null);
      // Reset search filters for private chat
      setSearchFilters({ organizationId: '', departmentId: '', name: '' });
      setAvailableDepartments([]);
      // Clear any existing search timeout
      if (searchTimeout) {
        clearTimeout(searchTimeout);
        setSearchTimeout(null);
      }
    }
  }, [isOpen, searchTimeout]);

  // Search for users using the task-assignment-users endpoint (for private chat)
  const searchUsers = async (filters = searchFilters) => {
    if (chatType !== 'private') return;

    if (!filters.organizationId && !filters.departmentId && !filters.name.trim()) {
      setUsers([]);
      return;
    }

    setIsSearching(true);
    try {
      const token = await getToken();
      if (!token) return;

      let url = '/api/v1/task-assignment-users?';
      const params = new URLSearchParams();

      // Always include organizationId for the new endpoint
      if (filters.organizationId) {
        params.append('organizationId', filters.organizationId);
      }

      if (filters.departmentId) {
        params.append('departmentId', filters.departmentId);
      }

      if (filters.name.trim()) {
        params.append('name', filters.name.trim());
      }

      const response = await fetch(url + params.toString(), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        const apiMembers = data.members || [];

        // Transform API response to match our User interface
        const transformedMembers: User[] = apiMembers
          .map((member: any) => ({
            id: member.user.id,
            name: `${member.user.firstName} ${member.user.lastName}`,
            firstName: member.user.firstName,
            lastName: member.user.lastName,
            email: member.user.email,
            imageUrl: member.user.imageUrl,
            departmentName: member.department?.name,
            organizationName: member.department?.organization?.name,
            isLeader: member.isLeader,
            isAdmin: member.isAdmin,
            isOwner: member.isOwner,
          }))
          .filter((user: User) => user.id !== currentUser?.id); // Exclude current user

        setUsers(transformedMembers);
      }
    } catch (err) {
      console.error('Error searching users:', err);
      setUsers([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle search filter changes with debouncing (for private chat)
  const handleSearchFilterChange = async (field: string, value: string) => {
    if (chatType !== 'private') return;

    const newFilters = { ...searchFilters, [field]: value };
    setSearchFilters(newFilters);

    // Clear existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // If organization changed, fetch departments for that organization
    if (field === 'organizationId' && value) {
      try {
        const token = await getToken();
        if (!token) return;

        const response = await fetch(`/api/v1/department?organizationId=${value}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setAvailableDepartments(data.departments || []);
        }
      } catch (err) {
        console.error('Error fetching departments for search:', err);
        setAvailableDepartments([]);
      }
    } else if (field === 'organizationId' && !value) {
      setAvailableDepartments([]);
      newFilters.departmentId = '';
      setSearchFilters(newFilters);
    }

    // Set up debounced search
    const timeout = setTimeout(() => {
      searchUsers(newFilters);
    }, 300);
    setSearchTimeout(timeout);
  };

  const handleItemSelect = (id: number) => {
    if (chatType === 'private') {
      // For private chats, allow multiple selection
      setSelectedItems(prev =>
        prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]
      );
    } else {
      // For other types, single selection
      setSelectedItems([id]);
    }
  };

  const getFilteredItems = () => {
    const query = searchQuery.toLowerCase();

    switch (chatType) {
      case 'private':
        return users.filter(user => {
          const fullName = `${user.firstName || ''} ${user.lastName || ''}`.toLowerCase();
          const email = (user.email || '').toLowerCase();
          return fullName.includes(query) || email.includes(query);
        });
      case 'task':
        return tasks.filter(task => {
          const title = (task.taskTitle || '').toLowerCase();
          const description = (task.taskDescription || '').toLowerCase();
          return title.includes(query) || description.includes(query);
        });
      case 'department':
        return departments.filter(dept => {
          const name = (dept.name || '').toLowerCase();
          const description = (dept.description || '').toLowerCase();
          return name.includes(query) || description.includes(query);
        });
      case 'organization':
        return organizations.filter(org => {
          const name = (org.name || '').toLowerCase();
          const description = (org.description || '').toLowerCase();
          return name.includes(query) || description.includes(query);
        });
      default:
        return [];
    }
  };

  const handleCreateChat = async () => {
    // Check if selection is required
    if (chatType === 'task' && !taskId && selectedItems.length === 0) {
      setError('Please select a task');
      return;
    } else if (selectedItems.length === 0 && chatType !== 'task') {
      setError('Please select at least one item');
      return;
    }

    // Additional validation for department chats
    if (chatType === 'department' && userData?.role?.isOwner && !selectedOrganizationId) {
      setError('Please select an organization first');
      return;
    }

    setCreating(true);
    setError('');

    try {
      let chatData: any = {
        chatType: chatType.toUpperCase(),
      };

      // Check for duplicate chats
      let existingChat = null;

      switch (chatType) {
        case 'private':
          chatData.participantIds = selectedItems;
          chatData.name = users
            .filter(user => selectedItems.includes(user.id))
            .map(user => `${user.firstName} ${user.lastName}`)
            .join(', ');

          // Check if private chat with these users already exists
          existingChat = await chatApi.checkChatExists({
            chatType: 'private',
            participantIds: [currentUser!.id, ...selectedItems],
          });
          break;

        case 'task':
          let selectedTaskId = taskId;
          let selectedTaskData = task;

          if (!taskId) {
            // User selected a task from the list
            if (selectedItems.length === 0) {
              setError('Please select a task');
              return;
            }
            selectedTaskId = selectedItems[0];
            selectedTaskData = tasks.find(t => t.id === selectedItems[0]) || null;
          }

          if (!selectedTaskId) {
            setError('Task ID is required');
            return;
          }

          chatData.taskId = selectedTaskId;
          chatData.name = selectedTaskData?.taskTitle || `Task Chat`;

          // Check if task chat already exists
          existingChat = await chatApi.checkChatExists({
            chatType: 'task',
            taskId: selectedTaskId,
          });
          break;

        case 'department':
          const selectedDept = departments.find(d => d.id === selectedItems[0]);
          if (!selectedDept) {
            setError('Selected department not found');
            return;
          }
          chatData.departmentId = selectedItems[0];

          // Get organization name for the new naming convention
          let organizationName = '';
          if (userData?.role?.isOwner && selectedOrganizationId) {
            const selectedOrg = availableOrganizations.find(org => org.id === selectedOrganizationId);
            organizationName = selectedOrg?.name || '';
            chatData.organizationId = selectedOrganizationId;
          } else if (selectedDept.organizationId) {
            // For members, try to get organization name from available data
            const orgFromUserData = userData?.organizations?.find(org => org.id === selectedDept.organizationId);
            organizationName = orgFromUserData?.name || '';
            chatData.organizationId = selectedDept.organizationId;
          } else if (userData?.organization?.id) {
            // Fallback to user's organization
            organizationName = userData.organization.name || '';
            chatData.organizationId = userData.organization.id;
          }

          // Set name with new convention: "Department Name (Organization Name)"
          chatData.name = organizationName ? `${selectedDept.name} (${organizationName})` : selectedDept.name;

          // Check if department chat already exists
          existingChat = await chatApi.checkChatExists({
            chatType: 'department',
            departmentId: selectedItems[0],
            ...(chatData.organizationId && { organizationId: chatData.organizationId }),
          });
          break;

        case 'organization':
          const selectedOrg = organizations.find(o => o.id === selectedItems[0]);
          if (!selectedOrg) {
            setError('Selected organization not found');
            return;
          }
          chatData.organizationId = selectedItems[0];
          chatData.name = selectedOrg.name;

          // Check if organization chat already exists
          existingChat = await chatApi.checkChatExists({
            chatType: 'organization',
            organizationId: selectedItems[0],
          });
          break;
      }

      if (existingChat) {
        setError(`${chatType.charAt(0).toUpperCase() + chatType.slice(1)} chat already exists`);
        return;
      }

      const response = await chatApi.createChat(chatData);
      onChatCreated(response.chat);
      onClose();
    } catch (error: any) {
      setError(error.message || 'Failed to create chat');
    } finally {
      setCreating(false);
    }
  };

  const getModalTitle = () => {
    const icons = {
      private: <MessageCircle size={20} />,
      task: <CheckSquare size={20} />,
      department: <Users size={20} />,
      organization: <Building2 size={20} />,
    };

    return (
      <>
        {icons[chatType]}
        Create {chatType.charAt(0).toUpperCase() + chatType.slice(1)} Chat
      </>
    );
  };

  const renderItems = () => {
    if (chatType === 'task') {
      if (taskId && task) {
        // Show specific task when taskId is provided
        return (
          <ItemCard>
            <Avatar>
              <CheckSquare size={20} />
            </Avatar>
            <ItemInfo>
              <ItemName>{task?.taskTitle || 'Loading...'}</ItemName>
              <ItemDescription>{task?.taskDescription || 'Task chat'}</ItemDescription>
            </ItemInfo>
          </ItemCard>
        );
      } else {
        // Show task selection list when no taskId is provided
        const filteredTasks = tasks.filter(task => {
          const title = (task.taskTitle || '').toLowerCase();
          const description = (task.taskDescription || '').toLowerCase();
          const query = searchQuery.toLowerCase();
          return title.includes(query) || description.includes(query);
        });

        return filteredTasks.map((task: Task) => (
          <ItemCard
            key={task.id}
            $isSelected={selectedItems.includes(task.id)}
            onClick={() => handleItemSelect(task.id)}
          >
            <Avatar>
              <CheckSquare size={20} />
            </Avatar>
            <ItemInfo>
              <ItemName>{task.taskTitle || 'Unknown Task'}</ItemName>
              <ItemDescription>
                {task.taskDescription || 'No description'} • Status:{' '}
                {task.status?.displayName || 'Unknown'} • Assigned to:{' '}
                {task.assignedToUser
                  ? `${task.assignedToUser.firstName} ${task.assignedToUser.lastName}`
                  : 'Unassigned'}
              </ItemDescription>
            </ItemInfo>
          </ItemCard>
        ));
      }
    }

    const items = getFilteredItems();

    return items.map((item: any) => (
      <ItemCard
        key={item.id}
        $isSelected={selectedItems.includes(item.id)}
        onClick={() => handleItemSelect(item.id)}
      >
        <Avatar $src={item.imageUrl}>
          {!item.imageUrl &&
            (chatType === 'private'
              ? `${(item.firstName || '')[0] || ''}${(item.lastName || '')[0] || ''}`
              : (item.name || '')[0] || '?')}
        </Avatar>
        <ItemInfo>
          <ItemName>
            {chatType === 'private'
              ? `${item.firstName || ''} ${item.lastName || ''}`.trim() || 'Unknown User'
              : item.name || 'Unknown'}
          </ItemName>
          <ItemDescription>
            {chatType === 'private' ? (
              <>
                {item.email || 'No email'}
                {(item.isOwner || item.isAdmin || item.isLeader) && (
                  <span style={{ marginLeft: '8px', fontWeight: 'bold', color: appTheme.colors.primary }}>
                    {item.isOwner ? '(Owner)' : item.isAdmin ? '(Admin)' : '(Leader)'}
                  </span>
                )}
                {item.departmentName && (
                  <span style={{ marginLeft: '8px', color: appTheme.colors.text.secondary }}>
                    • {item.departmentName}
                  </span>
                )}
              </>
            ) : chatType === 'department' ? (
              item.description || '-'
            ) : (
              item.description || `${chatType} chat`
            )}
          </ItemDescription>
        </ItemInfo>
      </ItemCard>
    ));
  };

  return (
    <ModalOverlay $isOpen={isOpen} onClick={onClose}>
      <ModalContent onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>{getModalTitle()}</ModalTitle>
          <CloseButton onClick={onClose}>
            <X size={20} />
          </CloseButton>
        </ModalHeader>

        <ModalBody>
          {error && <ErrorMessage>{error}</ErrorMessage>}

          {chatType === 'private' && (
            <FilterSection>
              <FilterGrid>
                <div>
                  <FilterLabel>Organization</FilterLabel>
                  <FilterSelect
                    value={searchFilters.organizationId}
                    onChange={e => handleSearchFilterChange('organizationId', e.target.value)}
                  >
                    <option value="">All Organizations</option>
                    {availableOrganizations.map(org => (
                      <option key={org.id} value={org.id}>
                        {org.name}
                      </option>
                    ))}
                  </FilterSelect>
                </div>
                <div>
                  <FilterLabel>Department (Optional)</FilterLabel>
                  <FilterSelect
                    value={searchFilters.departmentId}
                    onChange={e => handleSearchFilterChange('departmentId', e.target.value)}
                    disabled={!searchFilters.organizationId}
                  >
                    <option value="">All Departments</option>
                    {availableDepartments.map(dept => (
                      <option key={dept.id} value={dept.id}>
                        {dept.name}
                      </option>
                    ))}
                  </FilterSelect>
                </div>
              </FilterGrid>
              <div style={{ marginTop: '12px' }}>
                <FilterLabel>Search by Name</FilterLabel>
                <SearchInput
                  type="text"
                  placeholder="Search users by name or email..."
                  value={searchFilters.name}
                  onChange={e => handleSearchFilterChange('name', e.target.value)}
                />
              </div>
            </FilterSection>
          )}

          {chatType === 'department' && userData?.role?.isOwner && (
            <FilterSection>
              <FilterLabel>Organization</FilterLabel>
              <FilterSelect
                value={selectedOrganizationId || ''}
                onChange={e => {
                  const orgId = e.target.value ? parseInt(e.target.value) : null;
                  setSelectedOrganizationId(orgId);
                }}
              >
                <option value="">Select Organization</option>
                {availableOrganizations.map(org => (
                  <option key={org.id} value={org.id}>
                    {org.name}
                  </option>
                ))}
              </FilterSelect>
            </FilterSection>
          )}

          {(chatType !== 'task' || (chatType === 'task' && !taskId)) && chatType !== 'private' && (
            <SearchInput
              type="text"
              placeholder={`Search ${chatType === 'task' ? 'tasks' : chatType + 's'}...`}
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
            />
          )}

          {loading || (chatType === 'private' && isSearching) ? (
            <LoadingSpinner>{chatType === 'private' && isSearching ? 'Searching users...' : 'Loading...'}</LoadingSpinner>
          ) : (
            <ItemList>
              {renderItems()}
              {(chatType === 'task' && !taskId && tasks.length === 0) ||
              (chatType !== 'task' && getFilteredItems().length === 0) ||
              (chatType === 'task' && !taskId && getFilteredItems().length === 0) ? (
                <EmptyState>
                  <EmptyStateIcon>
                    {chatType === 'private' ? (
                      <Users size={40} />
                    ) : chatType === 'task' ? (
                      <CheckSquare size={40} />
                    ) : (
                      <Building2 size={40} />
                    )}
                  </EmptyStateIcon>
                  <EmptyStateText>
                    No{' '}
                    {chatType === 'private'
                      ? 'members'
                      : chatType === 'task'
                        ? 'tasks'
                        : chatType + 's'}{' '}
                    found.
                  </EmptyStateText>
                </EmptyState>
              ) : null}
            </ItemList>
          )}
        </ModalBody>

        <ModalFooter>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            $variant="primary"
            onClick={handleCreateChat}
            disabled={
              creating ||
              (chatType === 'task' && !taskId && selectedItems.length === 0) ||
              (chatType !== 'task' && selectedItems.length === 0)
            }
          >
            {creating ? 'Creating...' : 'Create Chat'}
          </Button>
        </ModalFooter>
      </ModalContent>
    </ModalOverlay>
  );
}
